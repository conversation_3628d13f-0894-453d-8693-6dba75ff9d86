from scrapers.base_scraper import Base<PERSON><PERSON>raper
import logging
import re
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse
import json

class IndeedScraper(BaseScraper):
    def __init__(self, config, db_manager, processor):
        super().__init__(config, db_manager, processor)
        self.site_name = "Indeed"
        self.seen_jobs = set()  # Track seen jobs to prevent duplicates
        
    def extract_job_listings(self, soup, site_name):
        """Extract job listings using Indeed-specific patterns"""
        
        # Indeed-specific selectors (try local patterns first)
        job_containers = soup.select('div.job_seen_beacon')
        
        if not job_containers:
            # Fallback selectors
            job_containers = soup.select('div[data-jk]') or soup.select('div.jobsearch-SerpJobCard')
            
        if not job_containers:
            logging.warning("No Indeed job containers found with local patterns, falling back to AI")
            return self._fallback_to_ai(soup, site_name)
            
        logging.info(f"Found {len(job_containers)} Indeed job containers using local patterns")
        
        new_jobs = []
        duplicate_count = 0

        for container in job_containers:
            try:
                job = self._extract_job_from_container(container, soup)
                if job and job.get('title') and job.get('url'):
                    # Create job signature for duplicate detection
                    job_signature = f"{job['title']}|{job['company']}"

                    if job_signature not in self.seen_jobs:
                        self.seen_jobs.add(job_signature)
                        new_jobs.append(job)
                        logging.info(f"Added Indeed job: {job['title']} at {job['company']}")
                    else:
                        duplicate_count += 1

            except Exception as e:
                logging.error(f"Error extracting Indeed job: {e}")

        # Log duplicate detection results
        if duplicate_count > 0:
            logging.info(f"Indeed: Found {duplicate_count} duplicate jobs, {len(new_jobs)} new jobs")

        logging.info(f"Extracted {len(new_jobs)} jobs from Indeed using local patterns")
        return new_jobs
    
    def _extract_job_from_container(self, container, _soup):
        """Extract job data from Indeed container using local patterns"""
        job = {}
        
        # Extract title
        title_elem = (container.select_one('h2.jobTitle a span') or 
                     container.select_one('h2 a span[title]') or
                     container.select_one('.jobTitle a'))
        job['title'] = title_elem.get('title') or title_elem.get_text(strip=True) if title_elem else ""
        
        # Extract company
        company_elem = (container.select_one('span.companyName a') or 
                       container.select_one('span.companyName') or
                       container.select_one('[data-testid="company-name"]'))
        job['company'] = company_elem.get_text(strip=True) if company_elem else ""
        
        # Extract location
        location_elem = (container.select_one('[data-testid="job-location"]') or
                        container.select_one('.companyLocation'))
        job['location'] = location_elem.get_text(strip=True) if location_elem else ""
        
        # Extract URL
        url_elem = (container.select_one('h2.jobTitle a') or 
                   container.select_one('h2 a'))
        if url_elem and url_elem.get('href'):
            job['url'] = urljoin('https://de.indeed.com', url_elem.get('href'))
        else:
            job['url'] = ""
            
        # Extract description/summary
        desc_elem = (container.select_one('.summary') or 
                    container.select_one('[data-testid="job-snippet"]'))
        job['description'] = desc_elem.get_text(strip=True) if desc_elem else ""
        
        # Extract salary if available
        salary_elem = container.select_one('.salary-snippet')
        job['salary'] = salary_elem.get_text(strip=True) if salary_elem else ""
        
        # Set source site
        job['source_site'] = 'Indeed'
        
        # Extract city from location
        job['city'] = self._extract_city_from_location(job['location'])
        
        return job
    
    def extract_next_page(self, soup, current_url):
        """Extract next page URL using Indeed-specific patterns"""
        # Try local patterns first
        next_page_url = self._extract_next_page_local(soup, current_url)
        
        if next_page_url:
            logging.info(f"Found next page using local patterns: {next_page_url}")
            return next_page_url
            
        # Fallback to AI if local patterns fail
        logging.info("Local pagination patterns failed, falling back to AI")
        return self._fallback_pagination_to_ai(soup, current_url)
    
    def _extract_next_page_local(self, soup, current_url):
        """Extract next page using Indeed-specific local patterns"""
        # Method 1: Look for "Next" link
        next_link = soup.select_one('a[aria-label="Next Page"]') or soup.select_one('a[aria-label="Next"]')
        if next_link and next_link.get('href'):
            return urljoin(current_url, next_link.get('href'))
            
        # Method 2: Look for pagination with page numbers
        pagination_links = soup.select('nav[role="navigation"] a')
        current_page = 1
        
        # Try to find current page
        current_page_elem = soup.select_one('nav[role="navigation"] span[aria-current="page"]')
        if current_page_elem:
            try:
                current_page = int(current_page_elem.get_text(strip=True))
            except ValueError:
                pass
                
        # Look for next page number
        for link in pagination_links:
            try:
                page_num = int(link.get_text(strip=True))
                if page_num == current_page + 1:
                    return urljoin(current_url, link.get('href'))
            except ValueError:
                continue
                
        # Method 3: Construct next page URL from current URL
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)
        
        # Get current start parameter (Indeed uses 'start' for pagination)
        current_start = int(query_params.get('start', ['0'])[0])
        next_start = current_start + 10  # Indeed typically shows 10 jobs per page
        
        # Update start parameter
        query_params['start'] = [str(next_start)]
        
        # Reconstruct URL
        new_query = urlencode(query_params, doseq=True)
        next_url = urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))
        
        # Only return if we actually incremented the start parameter and within reasonable limits
        if next_start > current_start and next_start <= 100:  # Limit to ~10 pages
            return next_url

        return None
    
    def _extract_city_from_location(self, location):
        """Extract city from location string"""
        if not location:
            return ""
            
        # Common patterns: "City, State", "City (State)", "Remote in City"
        location_parts = location.split(',')
        if len(location_parts) > 0:
            city = location_parts[0].strip()
            # Remove "Remote in" or similar prefixes
            city = re.sub(r'^(Remote in|Remote|Hybrid|Home Office in)\s*', '', city, flags=re.IGNORECASE)
            return city.strip()
            
        return ""

    def reset_seen_jobs(self):
        """Reset seen jobs tracker (call this when starting a new scraping session)"""
        self.seen_jobs.clear()

    def _fallback_to_ai(self, soup, site_name):
        """Fallback to AI extraction when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_job_listings(soup, site_name)
    
    def _fallback_pagination_to_ai(self, soup, current_url):
        """Fallback to AI pagination when local patterns fail"""
        from scrapers.ai_scraper import AIScraper
        ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
        return ai_scraper.extract_next_page(soup, current_url)
