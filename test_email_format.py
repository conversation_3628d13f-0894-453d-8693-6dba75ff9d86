#!/usr/bin/env python3
"""
Test script to demonstrate the improved email formatting for HubReport
"""

def format_analysis_for_html(analysis: str) -> str:
    """
    Format the AI analysis for HTML email display
    """
    # Split into lines and process each line
    lines = analysis.split('\n')
    formatted_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            formatted_lines.append('<br>')
        elif line.startswith('**') and line.endswith('**'):
            # Bold headers
            formatted_lines.append(f'<h3 style="color: #667eea; margin: 20px 0 10px 0;">{line[2:-2]}</h3>')
        elif line.startswith('- **') and ':**' in line:
            # Bold list items with descriptions
            parts = line.split(':**', 1)
            if len(parts) == 2:
                bold_part = parts[0][3:]  # Remove "- **"
                desc_part = parts[1]
                formatted_lines.append(f'<p style="margin: 8px 0;"><strong style="color: #667eea;">{bold_part}:</strong>{desc_part}</p>')
            else:
                formatted_lines.append(f'<p style="margin: 8px 0;">{line}</p>')
        elif line.startswith('- '):
            # Regular list items
            formatted_lines.append(f'<p style="margin: 8px 0; padding-left: 20px;">• {line[2:]}</p>')
        else:
            # Regular paragraphs
            formatted_lines.append(f'<p style="margin: 10px 0;">{line}</p>')
    
    return '\n'.join(formatted_lines)

# Sample AI analysis text (similar to what o3 would generate)
sample_analysis = """**ACME TECH SOLUTIONS - Berlin**

- **Growth Indicators:** Company shows rapid expansion with 5 unique technical positions posted in the last week, indicating aggressive scaling
- **Technical Needs:** Full-stack development, DevOps automation, and mobile app development
- **Outsourcing Potential:** High potential for backend development and infrastructure automation projects
- **Engagement Strategy:** Focus on scalability challenges and technical debt reduction
- **Priority Level:** High

**INNOVATE GMBH - Munich**

- **Growth Indicators:** Steady hiring pattern with focus on senior-level positions
- **Technical Needs:** Cloud migration, microservices architecture, and data analytics
- **Outsourcing Potential:** Medium potential for cloud infrastructure and data pipeline development
- **Engagement Strategy:** Emphasize cost-effective cloud solutions and modern architecture
- **Priority Level:** Medium

Key recommendations for business development outreach:

- Target companies showing multiple senior-level hires as they indicate budget availability
- Focus on technical modernization pain points for established companies
- Emphasize rapid scaling solutions for growing startups"""

def main():
    print("=== HubReport Email Formatting Test ===")
    print()
    
    print("ORIGINAL AI ANALYSIS:")
    print("-" * 50)
    print(sample_analysis)
    print()
    
    print("FORMATTED FOR HTML EMAIL:")
    print("-" * 50)
    formatted = format_analysis_for_html(sample_analysis)
    print(formatted)
    print()
    
    print("=== Email Preview ===")
    print("The formatted version will display in the email as:")
    print("• Bold company headers in blue")
    print("• Structured bullet points with colored labels")
    print("• Proper spacing and typography")
    print("• Professional business report appearance")

if __name__ == "__main__":
    main()
