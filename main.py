import asyncio
import logging
import sys

from database.db_manager import DatabaseManager
from processors.job_processor import JobProcessor
from scrapers.scraper_factory import ScraperFactory
from simple_cleaner import clean_duplicates
from email_sender import EmailSender
from datetime import datetime
from heroku_config import load_heroku_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("job_scraper.log"),
        logging.StreamHandler()
    ]
)

def load_config():
    """Load configuration - uses Heroku config on Heroku, local config otherwise"""
    try:
        return load_heroku_config()
    except Exception as e:
        logging.error(f"Failed to load configuration: {e}")
        raise

async def run_scrapers(config, db_manager, processor, test_link_id=None):
    """Run the AI scraper for all active links or a specific test link"""
    # Initialize statistics
    stats = {
        'test_mode': test_link_id is not None,
        'site_results': [],
        'total_jobs_scraped': 0,
        'sites_processed': 0
    }

    if test_link_id:
        # Test mode: scrape only the specified link
        links = db_manager.get_scraping_links()
        test_link = None

        for link in links:
            if link['id'] == test_link_id:
                test_link = link
                break

        if not test_link:
            logging.error(f"Test link with ID {test_link_id} not found!")
            logging.info("Available links:")
            for link in links:
                logging.info(f"  ID {link['id']}: {link['site_name']} ({link['city']}) - {link['url']}")
            return stats

        links = [test_link]
        logging.info(f"🧪 TEST MODE: Scraping only link ID {test_link_id}")
        logging.info(f"   Site: {test_link['site_name']}")
        logging.info(f"   City: {test_link['city']}")
        logging.info(f"   URL: {test_link['url']}")
    else:
        # Normal mode: scrape all links
        links = db_manager.get_scraping_links()
        logging.info(f"🚀 FULL MODE: Scraping all {len(links)} links")

    # Process each link
    for link in links:
        try:
            # Track total jobs before scraping this site
            cursor = db_manager.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM jobs")
            total_jobs_before = cursor.fetchone()[0]

            # Create appropriate scraper for this site
            site_name = link.get('site_name', 'Unknown')
            scraper = ScraperFactory.create_scraper(site_name, config, db_manager, processor)

            # Reset any site-specific state (e.g., Glassdoor seen jobs)
            if hasattr(scraper, 'reset_seen_jobs'):
                scraper.reset_seen_jobs()

            # Run scraper for this link and get page count
            pages_scraped = await scraper.run_scraper(link)

            # Track total jobs after scraping this site
            cursor.execute("SELECT COUNT(*) FROM jobs")
            total_jobs_after = cursor.fetchone()[0]

            # Calculate jobs added for this site
            jobs_added = total_jobs_after - total_jobs_before

            # Add site statistics
            site_stats = {
                'site_name': link['site_name'],
                'city': link['city'],
                'url': link['url'],
                'jobs_found': jobs_added,
                'new_jobs': jobs_added,  # All jobs are "new" since we don't check duplicates
                'updated_jobs': 0,  # No updates during scraping
                'pages_scraped': pages_scraped or 1  # Use actual page count, fallback to 1
            }

            stats['site_results'].append(site_stats)
            stats['total_jobs_scraped'] += jobs_added
            stats['sites_processed'] += 1

            logging.info(f"Site {link['site_name']} completed: {jobs_added} jobs added")

        except Exception as e:
            logging.error(f"Error scraping {link['url']}: {e}")

    return stats

def job(test_link_id=None):
    """Main job function to be scheduled"""
    start_time = datetime.now()

    if test_link_id:
        logging.info(f"Starting TEST scraping run for link ID {test_link_id}")
    else:
        logging.info("Starting FULL scraping run")

    config = load_config()
    db_manager = DatabaseManager(config)
    processor = JobProcessor(config)

    # Initialize summary data
    summary_data = {
        'test_mode': test_link_id is not None,
        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
        'site_results': [],
        'total_jobs_scraped': 0,
        'sites_processed': 0,
        'cleanup_results': {}
    }

    try:
        # Run scrapers and collect statistics
        scraping_stats = asyncio.run(run_scrapers(config, db_manager, processor, test_link_id))

        # Update summary with scraping results
        summary_data.update(scraping_stats)

        # Clean duplicates after scraping
        logging.info("Starting duplicate cleanup...")
        cleanup_results = clean_duplicates()
        summary_data['cleanup_results'] = cleanup_results or {'before': 0, 'after': 0, 'removed': 0}
        logging.info("Duplicate cleanup completed")

        # Calculate end time and duration
        end_time = datetime.now()
        duration = end_time - start_time
        summary_data['end_time'] = end_time.strftime('%Y-%m-%d %H:%M:%S')
        summary_data['duration'] = str(duration).split('.')[0]  # Remove microseconds

        # Send email summary
        try:
            email_sender = EmailSender(config)
            email_sent = email_sender.send_summary_email(summary_data)
            if email_sent:
                logging.info("Summary email sent successfully")
            else:
                logging.warning("Failed to send summary email")
        except Exception as e:
            logging.error(f"Error sending summary email: {e}")

    except Exception as e:
        logging.error(f"Error in scraping job: {e}")

        # Send error email
        try:
            end_time = datetime.now()
            duration = end_time - start_time
            error_summary = {
                'test_mode': test_link_id is not None,
                'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': str(duration).split('.')[0],
                'error': str(e),
                'site_results': [],
                'total_jobs_scraped': 0,
                'sites_processed': 0,
                'cleanup_results': {'before': 0, 'after': 0, 'removed': 0}
            }

            email_sender = EmailSender(config)
            email_sender.send_summary_email(error_summary)
        except:
            pass  # Don't fail if error email fails

    finally:
        db_manager.close()

    if test_link_id:
        logging.info(f"Completed TEST scraping run for link ID {test_link_id}")
    else:
        logging.info("Completed FULL scraping run")

def main():
    """Main function - run once and exit"""
    # Check for test mode argument
    test_link_id = None

    if len(sys.argv) > 1:
        try:
            test_link_id = int(sys.argv[1])
            print(f"🧪 TEST MODE: Will scrape only link ID {test_link_id}")
        except ValueError:
            print(f"❌ Error: '{sys.argv[1]}' is not a valid link ID number")
            print("Usage:")
            print("  python main.py           # Scrape all links")
            print("  python main.py <link_id> # Test scrape specific link")
            return
    else:
        print("🚀 FULL MODE: Will scrape all links")

    job(test_link_id)

if __name__ == "__main__":
    main()

