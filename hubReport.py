#!/usr/bin/env python3
"""
HubReport - Advanced Job Market Analysis for Outsourcing Opportunities
Analyzes job postings to identify companies that might benefit from outsourcing services
"""
import asyncio
import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json

from database.db_manager import DatabaseManager
from email_sender import EmailSender
from heroku_config import load_heroku_config
from openai import OpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hub_report.log"),
        logging.StreamHandler()
    ]
)

class HubReportAnalyzer:
    def __init__(self, config, hub_config=None):
        self.config = config
        self.hub_config = hub_config or {}
        self.db_manager = DatabaseManager(config)
        self.email_sender = EmailSender(config)
        self.openai_client = OpenAI(api_key=config['openai']['api_key'])
        
    async def get_companies_with_multiple_jobs(self, days_back: int = 7, min_jobs: int = 3) -> List[Dict]:
        """
        Get companies that have posted multiple unique job positions in the specified timeframe
        """
        query = """
        WITH JobsWithPrevious AS (
            SELECT
                id,
                company,
                title,
                city,
                location,
                created_at,
                source_site,
                LAG(created_at) OVER (
                    PARTITION BY company, title, city
                    ORDER BY created_at
                ) as prev_timestamp
            FROM jobs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                AND company IS NOT NULL
                AND company != ''
                AND title IS NOT NULL
                AND title != ''
        ),
        UniqueJobs AS (
            SELECT
                id,
                company,
                title,
                city,
                location,
                created_at,
                source_site
            FROM JobsWithPrevious
            WHERE
                prev_timestamp IS NULL
                OR TIMESTAMPDIFF(HOUR, prev_timestamp, created_at) >= 48
        ),
        CompaniesWithMultipleJobs AS (
            SELECT
                company,
                city,
                COUNT(DISTINCT title) as unique_positions,
                COUNT(*) as total_jobs
            FROM UniqueJobs
            GROUP BY company, city
            HAVING COUNT(DISTINCT title) >= %s
        )
        SELECT
            uj.id,
            uj.company,
            uj.title,
            uj.city,
            uj.location,
            uj.created_at,
            uj.source_site,
            c.unique_positions,
            c.total_jobs
        FROM UniqueJobs uj
        INNER JOIN CompaniesWithMultipleJobs c
            ON uj.company = c.company
            AND uj.city = c.city
        ORDER BY c.unique_positions DESC, c.total_jobs DESC, uj.city, uj.company, uj.created_at DESC
        """
        
        cursor = self.db_manager.connection.cursor(dictionary=True)
        cursor.execute(query, (days_back, min_jobs))
        results = cursor.fetchall()
        cursor.close()
        
        logging.info(f"Found {len(results)} jobs from companies with {min_jobs}+ unique positions in the last {days_back} days")
        return results

    def group_jobs_by_company_and_city(self, jobs: List[Dict]) -> Dict[str, Dict]:
        """
        Group jobs by company and city for analysis
        """
        grouped = {}
        
        for job in jobs:
            company_key = f"{job['company']} ({job['city']})"
            
            if company_key not in grouped:
                grouped[company_key] = {
                    'company': job['company'],
                    'city': job['city'],
                    'unique_positions': job['unique_positions'],
                    'total_jobs': job['total_jobs'],
                    'jobs': [],
                    'source_sites': set()
                }
            
            grouped[company_key]['jobs'].append(job)
            grouped[company_key]['source_sites'].add(job['source_site'])
        
        return grouped

    async def analyze_companies_by_city(self, city_companies: List[Dict], city_name: str, model: str) -> str:
        """
        Analyze companies for a specific city
        """
        # Enhanced AI prompt for city-specific analysis
        prompt = f"""
        As a business development expert specializing in IT outsourcing, analyze the following companies in {city_name} based on their recent job postings.

        ANALYSIS CRITERIA:
        - Focus on companies with 3+ unique software development positions
        - Exclude: Major corporations (10,000+ employees), recruitment agencies, government entities, educational institutions
        - Prioritize: Growing companies, startups, scale-ups, mid-size businesses showing expansion
        - Look for: Companies posting multiple technical roles indicating growth or team scaling

        COMPANY DATA FOR {city_name.upper()}:
        {json.dumps(city_companies, indent=2, default=str)}

        REQUIRED OUTPUT FORMAT:
        For each promising company, provide:

        **[COMPANY NAME] - {city_name}**
        - **Growth Indicators:** [Why this company shows growth/scaling signs]
        - **Technical Needs:** [Summary of technical roles they're hiring for]
        - **Outsourcing Potential:** [Specific areas where outsourcing could help]
        - **Engagement Strategy:** [How to approach them - specific pain points to address]
        - **Priority Level:** [High/Medium/Low based on opportunity size]

        EXCLUSION CRITERIA:
        - Skip obvious recruitment agencies (keywords: "recruitment", "staffing", "talent", "headhunting")
        - Skip major corporations with 10,000+ employees
        - Skip government agencies and educational institutions
        - Skip companies with only 1-2 job postings

        Focus on actionable insights for business development outreach specific to the {city_name} market.
        """

        try:
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=model,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"OpenAI analysis failed for {city_name}: {e}")
            return f"AI Analysis failed for {city_name}: {str(e)}"

    async def analyze_companies_with_ai(self, companies_data: Dict[str, Dict]) -> str:
        """
        Use OpenAI to analyze companies and identify outsourcing opportunities
        Split by cities to optimize token usage and improve analysis quality
        """
        # Get AI configuration from hub_config
        ai_config = self.hub_config.get('ai_analysis', {})
        model = ai_config.get('model', 'gpt-4o')
        max_companies_per_request = ai_config.get('max_companies_per_request', 8)

        # Get filtering configuration
        filters = self.hub_config.get('filters', {})
        excluded_companies = filters.get('excluded_companies', [])
        recruitment_keywords = filters.get('recruitment_keywords', [])

        # Group companies by city and apply filtering
        cities_data = {}
        total_filtered_companies = 0

        for _, data in companies_data.items():
            # Apply filtering based on configuration
            company_name_lower = data['company'].lower()

            # Skip if company matches exclusion criteria
            if any(excluded.lower() in company_name_lower for excluded in excluded_companies):
                continue
            if any(keyword.lower() in company_name_lower for keyword in recruitment_keywords):
                continue

            city = data['city']
            if city not in cities_data:
                cities_data[city] = []

            company_info = {
                'company_name': data['company'],
                'city': data['city'],
                'unique_positions': data['unique_positions'],
                'total_jobs': data['total_jobs'],
                'job_titles': [job['title'] for job in data['jobs']],
                'source_sites': list(data['source_sites']),
                'recent_postings': len([job for job in data['jobs'] if
                    (datetime.now() - job['created_at']).days <= 3])
            }
            cities_data[city].append(company_info)
            total_filtered_companies += 1

        logging.info(f"Analyzing {total_filtered_companies} companies across {len(cities_data)} cities")

        # Prepare all analysis tasks for parallel processing
        analysis_tasks = []

        for city, city_companies in cities_data.items():
            # If city has too many companies, split into chunks
            if len(city_companies) > max_companies_per_request:
                logging.info(f"Splitting {city} ({len(city_companies)} companies) into chunks of {max_companies_per_request}")

                for i in range(0, len(city_companies), max_companies_per_request):
                    chunk = city_companies[i:i + max_companies_per_request]
                    chunk_name = f"{city} (Part {i//max_companies_per_request + 1})"
                    analysis_tasks.append((chunk, chunk_name, model))
            else:
                analysis_tasks.append((city_companies, city, model))

        logging.info(f"Processing {len(analysis_tasks)} analysis tasks in parallel (3 concurrent requests)")

        # Process tasks in parallel batches of 3
        city_analyses = []
        openai_requests_made = 0

        for i in range(0, len(analysis_tasks), 3):
            batch = analysis_tasks[i:i + 3]
            batch_tasks = []

            for companies, city_name, model_name in batch:
                logging.info(f"Analyzing {city_name} with {len(companies)} companies...")
                task = self.analyze_companies_by_city(companies, city_name, model_name)
                batch_tasks.append((task, city_name))

            # Run batch of up to 3 requests in parallel
            batch_results = await asyncio.gather(*[task for task, _ in batch_tasks])

            # Collect results
            for j, (result, city_name) in enumerate(zip(batch_results, [name for _, name in batch_tasks])):
                city_analyses.append(f"\n=== {city_name.upper()} ===\n{result}")
                openai_requests_made += 1

        logging.info(f"Completed analysis using {openai_requests_made} OpenAI requests")

        # Combine and clean up city analyses for sales team
        city_content_map = {}

        for analysis in city_analyses:
            lines = analysis.split('\n')
            current_city = None

            for line in lines:
                if line.startswith('=== ') and line.endswith(' ==='):
                    # Extract city name and remove part numbers
                    city_header = line.strip('= ')
                    if '(Part' in city_header:
                        current_city = city_header.split(' (Part')[0].strip()
                    else:
                        current_city = city_header.strip()

                    # Initialize city content if not exists
                    if current_city not in city_content_map:
                        city_content_map[current_city] = []

                elif current_city and line.strip():  # Only add non-empty lines
                    city_content_map[current_city].append(line)

        # Create consolidated city analyses
        cleaned_analyses = []
        for city, content in city_content_map.items():
            if content:  # Only add cities with content
                cleaned_analyses.append(f"\n=== {city.upper()} ===\n" + '\n'.join(content))

        # Create sales-focused summary
        combined_analysis = "\n".join(cleaned_analyses)

        # Create clean summary without technical details
        summary = f"""
MARKET OPPORTUNITY SUMMARY:
• {total_filtered_companies} High-Potential Companies Identified
• {len(cities_data)} Strategic Markets Analyzed
• Focus: Companies with 3+ Technical Positions (Growth Indicators)

TOP MARKETS BY OPPORTUNITY:
{chr(10).join([f"• {city}: {len(companies)} companies" for city, companies in sorted(cities_data.items(), key=lambda x: len(x[1]), reverse=True)[:10]])}

{combined_analysis}

NEXT STEPS:
• Review high-priority companies for immediate outreach
• Focus on companies showing rapid growth indicators
• Prioritize markets with multiple opportunities
        """

        return summary.strip()

    def generate_summary_stats(self, companies_data: Dict[str, Dict]) -> Dict:
        """
        Generate summary statistics for the report
        """
        total_companies = len(companies_data)
        total_positions = sum(data['unique_positions'] for data in companies_data.values())
        total_jobs = sum(data['total_jobs'] for data in companies_data.values())
        
        # Group by city
        cities = {}
        for data in companies_data.values():
            city = data['city']
            if city not in cities:
                cities[city] = {'companies': 0, 'positions': 0, 'jobs': 0}
            cities[city]['companies'] += 1
            cities[city]['positions'] += data['unique_positions']
            cities[city]['jobs'] += data['total_jobs']
        
        # Top cities by opportunity
        top_cities = sorted(cities.items(), key=lambda x: x[1]['companies'], reverse=True)[:5]
        
        return {
            'total_companies': total_companies,
            'total_unique_positions': total_positions,
            'total_jobs': total_jobs,
            'cities_analyzed': len(cities),
            'top_cities': top_cities,
            'avg_positions_per_company': round(total_positions / total_companies, 1) if total_companies > 0 else 0
        }

    async def save_analysis_to_db(self, analysis_text: str, stats: Dict, date_range_start: datetime, date_range_end: datetime):
        """
        Save the analysis results to database
        """
        try:
            cursor = self.db_manager.connection.cursor()
            
            # Create table if it doesn't exist
            create_table_query = """
            CREATE TABLE IF NOT EXISTS hub_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date_range_start DATETIME NOT NULL,
                date_range_end DATETIME NOT NULL,
                companies_analyzed INT NOT NULL,
                total_positions INT NOT NULL,
                total_jobs INT NOT NULL,
                analysis_text MEDIUMTEXT NOT NULL,
                stats_json JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            cursor.execute(create_table_query)
            
            # Insert analysis
            insert_query = """
            INSERT INTO hub_reports (
                date_range_start, date_range_end, companies_analyzed, 
                total_positions, total_jobs, analysis_text, stats_json
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, (
                date_range_start,
                date_range_end,
                stats['total_companies'],
                stats['total_unique_positions'],
                stats['total_jobs'],
                analysis_text,
                json.dumps(stats)
            ))
            
            self.db_manager.connection.commit()
            cursor.close()
            
            logging.info("Analysis saved to database successfully")
            
        except Exception as e:
            logging.error(f"Failed to save analysis to database: {e}")

    def _format_analysis_for_html(self, analysis: str) -> str:
        """
        Format the AI analysis for HTML email display
        """
        # Split into lines and process each line
        lines = analysis.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                formatted_lines.append('<br>')
            elif line.startswith('**') and line.endswith('**'):
                # Bold headers
                formatted_lines.append(f'<h3 style="color: #667eea; margin: 20px 0 10px 0;">{line[2:-2]}</h3>')
            elif line.startswith('- **') and ':**' in line:
                # Bold list items with descriptions
                parts = line.split(':**', 1)
                if len(parts) == 2:
                    bold_part = parts[0][3:]  # Remove "- **"
                    desc_part = parts[1]
                    formatted_lines.append(f'<p style="margin: 8px 0;"><strong style="color: #667eea;">{bold_part}:</strong>{desc_part}</p>')
                else:
                    formatted_lines.append(f'<p style="margin: 8px 0;">{line}</p>')
            elif line.startswith('- '):
                # Regular list items
                formatted_lines.append(f'<p style="margin: 8px 0; padding-left: 20px;">• {line[2:]}</p>')
            else:
                # Regular paragraphs
                formatted_lines.append(f'<p style="margin: 10px 0;">{line}</p>')

        return '\n'.join(formatted_lines)

    async def send_report_email(self, analysis: str, stats: Dict, _date_range_start: datetime, date_range_end: datetime):
        """
        Send the analysis report via email
        """
        subject = f"Sales Opportunities - {stats['total_companies']} Companies - {date_range_end.strftime('%b %d')}"

        # Format analysis for HTML
        formatted_analysis = self._format_analysis_for_html(analysis)
        
        # Create clean HTML email content - sales focused
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1000px; margin: 0 auto; padding: 20px; }}
                .analysis {{ background: white; padding: 30px; border-radius: 10px; }}
                h2 {{ color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; margin-top: 0; }}
                h3 {{ color: #667eea; margin: 20px 0 10px 0; font-size: 1.3em; }}
                .analysis-content {{ background: #f8f9fa; padding: 20px; border-radius: 5px; line-height: 1.8; }}
                .analysis-content p {{ margin: 10px 0; }}
                .analysis-content strong {{ color: #667eea; }}
            </style>
        </head>
        <body>
            <div class="analysis">
                <h2>🎯 Sales Opportunities & Engagement Strategies</h2>
                <div class="analysis-content">
                    {formatted_analysis}
                </div>
            </div>
        </body>
        </html>
        """

        # Plain text version - clean and focused
        text_content = f"""
SALES OPPORTUNITIES & ENGAGEMENT STRATEGIES
{'=' * 50}

{analysis}

{'=' * 50}
Generated on {datetime.now().strftime('%B %d, %Y')}
        """

        # Override email content with our custom report
        original_create_html = self.email_sender._create_html_summary
        original_create_text = self.email_sender._create_text_summary
        
        self.email_sender._create_html_summary = lambda _: html_content
        self.email_sender._create_text_summary = lambda _: text_content
        
        # Override subject
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        
        try:
            # Get email recipients from config
            recipients = self.hub_config.get('emails', [self.email_sender.recipient])
            recipients_str = ', '.join(recipients)

            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.email_sender.sender
            msg['To'] = recipients_str

            # Attach parts
            part1 = MIMEText(text_content, 'plain')
            part2 = MIMEText(html_content, 'html')

            msg.attach(part1)
            msg.attach(part2)

            # Send email
            with smtplib.SMTP(self.email_sender.smtp_config['host'], self.email_sender.smtp_config['port']) as server:
                server.starttls()
                server.login(
                    self.email_sender.smtp_config['auth']['user'],
                    self.email_sender.smtp_config['auth']['pass']
                )
                server.send_message(msg)

            logging.info(f"HubReport email sent successfully to {recipients_str}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to send HubReport email: {e}")
            return False
        finally:
            # Restore original methods
            self.email_sender._create_html_summary = original_create_html
            self.email_sender._create_text_summary = original_create_text

    async def run_analysis(self, days_back: int = 7, min_jobs: int = 3):
        """
        Run the complete analysis workflow
        """
        try:
            logging.info("Starting HubReport analysis...")

            # Calculate date range
            date_range_end = datetime.now()
            date_range_start = date_range_end - timedelta(days=days_back)

            logging.info(f"Analyzing period: {date_range_start.strftime('%Y-%m-%d')} to {date_range_end.strftime('%Y-%m-%d')}")
            
            # Get companies with multiple job postings
            jobs = await self.get_companies_with_multiple_jobs(days_back, min_jobs)
            
            if not jobs:
                logging.warning("No companies found with multiple job postings")
                return
            
            # Group jobs by company and city
            companies_data = self.group_jobs_by_company_and_city(jobs)
            logging.info(f"Found {len(companies_data)} companies for analysis")

            # Generate summary statistics
            stats = self.generate_summary_stats(companies_data)

            # Analyze with AI
            logging.info("Running AI analysis...")
            analysis = await self.analyze_companies_with_ai(companies_data)

            # Save to database
            await self.save_analysis_to_db(analysis, stats, date_range_start, date_range_end)

            # Send email report
            logging.info("Sending email report...")
            email_sent = await self.send_report_email(analysis, stats, date_range_start, date_range_end)

            if email_sent:
                logging.info("HubReport analysis completed successfully!")
            else:
                logging.warning("Analysis completed but email sending failed")
                
        except Exception as e:
            logging.error(f"HubReport analysis failed: {e}")
            raise
        finally:
            self.db_manager.close()

def should_run_today() -> bool:
    """
    Check if analysis should run today (Mondays)
    """
    return datetime.now().weekday() == 0  # Monday = 0

async def main():
    """
    Main function
    """
    # Check for force run argument
    force_run = '--force' in sys.argv
    
    if not force_run and not should_run_today():
        logging.info("📅 Not scheduled to run today (runs on Mondays). Use --force to run anyway.")
        return
    
    try:
        # Load configuration
        config = load_heroku_config()
        
        # Create analyzer
        analyzer = HubReportAnalyzer(config)
        
        # Run analysis
        await analyzer.run_analysis()
        
    except Exception as e:
        logging.error(f"Critical error in HubReport: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
