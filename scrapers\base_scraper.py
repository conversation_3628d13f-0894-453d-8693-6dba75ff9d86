from scrapfly import ScrapeConfig, ScrapflyClient
import logging
from bs4 import BeautifulSoup
import time

class BaseScraper:
    def __init__(self, config, db_manager, processor):
        self.config = config
        self.db_manager = db_manager
        self.processor = processor
        self.scrapfly = ScrapflyClient(key=config['scrapfly']['api_key'])

    async def scrape_page(self, url, site_name, city=None):
        """Scrape a single page and extract job listings"""
        try:
            result = await self.scrapfly.async_scrape(ScrapeConfig(
                url=url,
                asp=True,  # Anti-scraping protection
                country="US"
            ))

            if result.status_code == 200:
                soup = BeautifulSoup(result.content, 'html.parser')

                # Extract job listings
                job_listings = self.extract_job_listings(soup, site_name)

                # Set city to exactly match the city from the scraping link
                if city:
                    for job in job_listings:
                        job['city'] = city  # Always use the city from the link

                # Process jobs with local pattern matching
                for job in job_listings:
                    # Process job data locally without API calls
                    processed_job = self.processor.process_job_locally(job)
                    self.db_manager.save_job(processed_job)

                # Find next page link if it exists
                next_page = self.extract_next_page(soup, url)
                return next_page
            else:
                logging.error(f"Failed to scrape {url}: Status code {result.status_code}")
                return None
        except Exception as e:
            logging.error(f"Error scraping {url}: {e}")
            return None

    def extract_job_listings(self, soup, site_name):
        """Extract job listings from the page - to be implemented by specific scrapers"""
        raise NotImplementedError("Subclasses must implement extract_job_listings")

    def extract_next_page(self, soup, current_url):
        """Extract next page URL - to be implemented by specific scrapers"""
        raise NotImplementedError("Subclasses must implement extract_next_page")

    async def run_scraper(self, link):
        """Run the scraper for a specific link"""
        url = link['url']
        site_name = link['site_name']
        city = link.get('city', '')

        logging.info(f"Starting scraping for {site_name}: {url}")

        page_count = 0
        seen_job_signatures = set()  # Track job signatures to detect duplicates
        consecutive_duplicate_pages = 0  # Track pages with mostly duplicates
        max_pages = 10  # Maximum pages to scrape

        while url and page_count < max_pages:
            page_count += 1
            logging.info(f"Scraping page {page_count}: {url}")

            # Get jobs count before scraping this page
            jobs_before = self.db_manager.get_jobs_count()

            next_page = await self.scrape_page(url, site_name, city)

            # Get jobs count after scraping this page
            jobs_after = self.db_manager.get_jobs_count()
            jobs_added_this_page = jobs_after - jobs_before

            # Check for duplicate detection (if very few jobs added, might be duplicates)
            if page_count > 1 and jobs_added_this_page < 3:
                consecutive_duplicate_pages += 1
                logging.info(f"Page {page_count}: Only {jobs_added_this_page} new jobs added (possible duplicates)")

                # If 2 consecutive pages add very few jobs, likely hitting duplicates
                if consecutive_duplicate_pages >= 2:
                    logging.info("Detected possible pagination loop (very few new jobs), stopping")
                    break
            else:
                consecutive_duplicate_pages = 0

            if next_page == url:
                logging.warning(f"Next page is the same as current page: {url}")
                break

            url = next_page
            if url:
                # Add a delay between requests to avoid overloading the server
                time.sleep(self.config['scraping']['delay_seconds'])

        logging.info(f"Completed scraping for {site_name}")
        return page_count
