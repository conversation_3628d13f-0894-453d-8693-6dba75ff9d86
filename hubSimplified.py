#!/usr/bin/env python3
"""
HubSimplified - Simple Company List Report
Based on HubReport but outputs only clean company lists per city without detailed analysis
"""
import asyncio
import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, List
import json

from database.db_manager import DatabaseManager
from email_sender import EmailSender
from heroku_config import load_heroku_config
from openai import OpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hub_simplified.log"),
        logging.StreamHandler()
    ]
)

class HubSimplifiedAnalyzer:
    def __init__(self, config, hub_config=None):
        self.config = config
        self.hub_config = hub_config or {}
        self.db_manager = DatabaseManager(config)
        self.email_sender = EmailSender(config)
        self.openai_client = OpenAI(api_key=config['openai']['api_key'])

    async def get_companies_with_multiple_jobs(self, days_back: int = 7, min_jobs: int = 3) -> List[Dict]:
        """
        Get companies that have posted multiple unique job positions in the specified timeframe
        """
        query = """
        WITH JobsWithPrevious AS (
            SELECT
                id,
                company,
                title,
                city,
                location,
                created_at,
                source_site,
                LAG(created_at) OVER (
                    PARTITION BY company, title, city
                    ORDER BY created_at
                ) as prev_timestamp
            FROM jobs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
                AND company IS NOT NULL
                AND company != ''
                AND title IS NOT NULL
                AND title != ''
        ),
        UniqueJobs AS (
            SELECT
                id,
                company,
                title,
                city,
                location,
                created_at,
                source_site
            FROM JobsWithPrevious
            WHERE
                prev_timestamp IS NULL
                OR TIMESTAMPDIFF(HOUR, prev_timestamp, created_at) >= 48
        ),
        CompaniesWithMultipleJobs AS (
            SELECT
                company,
                city,
                COUNT(DISTINCT title) as unique_positions,
                COUNT(*) as total_jobs
            FROM UniqueJobs
            GROUP BY company, city
            HAVING unique_positions >= %s
        )
        SELECT
            uj.id,
            uj.company,
            uj.title,
            uj.city,
            uj.location,
            uj.created_at,
            uj.source_site,
            c.unique_positions,
            c.total_jobs
        FROM UniqueJobs uj
        INNER JOIN CompaniesWithMultipleJobs c
            ON uj.company = c.company
            AND uj.city = c.city
        ORDER BY c.unique_positions DESC, c.total_jobs DESC, uj.city, uj.company, uj.created_at DESC
        """

        cursor = self.db_manager.connection.cursor(dictionary=True)
        cursor.execute(query, (days_back, min_jobs))
        results = cursor.fetchall()
        cursor.close()

        logging.info(f"Found {len(results)} jobs from companies with {min_jobs}+ unique positions in the last {days_back} days")
        return results

    def group_jobs_by_company_and_city(self, jobs: List[Dict]) -> Dict[str, Dict]:
        """
        Group jobs by company and city combination
        """
        companies_data = {}

        for job in jobs:
            key = f"{job['company']}_{job['city']}"

            if key not in companies_data:
                companies_data[key] = {
                    'company': job['company'],
                    'city': job['city'],
                    'unique_positions': job['unique_positions'],
                    'total_jobs': job['total_jobs'],
                    'jobs': [],
                    'source_sites': set()
                }

            companies_data[key]['jobs'].append(job)
            companies_data[key]['source_sites'].add(job['source_site'])

        # Convert sets to lists for JSON serialization
        for company_data in companies_data.values():
            company_data['source_sites'] = list(company_data['source_sites'])

        return companies_data

    def generate_summary_stats(self, companies_data: Dict[str, Dict]) -> Dict:
        """
        Generate summary statistics for the analysis
        """
        total_companies = len(companies_data)
        total_jobs = sum(len(data['jobs']) for data in companies_data.values())
        total_unique_positions = sum(data['unique_positions'] for data in companies_data.values())

        # Group by city for city-level stats
        cities = {}
        for data in companies_data.values():
            city = data['city']
            if city not in cities:
                cities[city] = {'companies': 0, 'jobs': 0, 'positions': 0}
            cities[city]['companies'] += 1
            cities[city]['jobs'] += len(data['jobs'])
            cities[city]['positions'] += data['unique_positions']

        return {
            'total_companies': total_companies,
            'total_jobs': total_jobs,
            'total_unique_positions': total_unique_positions,
            'cities': cities,
            'cities_count': len(cities)
        }

    async def analyze_companies_by_city_simplified(self, city_companies: List[Dict], city_name: str, model: str) -> str:
        """
        Simplified AI analysis that returns only company lists
        """
        # Prepare data for AI analysis (same as HubReport)
        analysis_data = []
        for company_key, company_data in city_companies.items():
            company_info = {
                'company': company_data['company'],
                'unique_positions': company_data['unique_positions'],
                'total_jobs': company_data['total_jobs'],
                'source_sites': company_data['source_sites'],
                'sample_jobs': [
                    {
                        'title': job['title'],
                        'location': job['location']
                    } for job in company_data['jobs'][:3]  # Sample jobs
                ]
            }
            analysis_data.append(company_info)

        # Simplified AI prompt - same filtering logic as HubReport but simple output
        prompt = f"""
        As a business development expert, analyze the following companies in {city_name} based on their recent job postings.

        ANALYSIS CRITERIA (same as HubReport):
        - Focus on companies with 3+ unique software development positions
        - Exclude: Major corporations (10,000+ employees), recruitment agencies, government entities, educational institutions
        - Prioritize: Growing companies, startups, scale-ups, mid-size businesses showing expansion
        - Look for: Companies posting multiple technical roles indicating growth or team scaling

        COMPANY DATA:
        {json.dumps(analysis_data, indent=2, default=str)}

        REQUIRED OUTPUT FORMAT:
        Return ONLY a simple bullet-point list of qualifying company names.
        Do NOT include explanations, analysis, or additional text.

        Example format:
        • Company Name 1
        • Company Name 2
        • Company Name 3

        EXCLUSION CRITERIA (same as HubReport):
        - Skip obvious recruitment agencies (keywords: "recruitment", "staffing", "talent", "headhunting")
        - Skip major corporations with 10,000+ employees
        - Skip government agencies and educational institutions
        - Skip companies with only 1-2 job postings

        Only list companies that represent genuine outsourcing opportunities.
        """

        try:
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=model,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"OpenAI analysis failed for {city_name}: {e}")
            return f"AI Analysis failed for {city_name}: {str(e)}"

    async def analyze_companies_with_ai_simplified(self, companies_data: Dict[str, Dict]) -> str:
        """
        Use OpenAI to analyze companies and identify outsourcing opportunities
        Split by cities to optimize token usage and improve analysis quality
        """
        # Get AI configuration from hub_config
        ai_config = self.hub_config.get('ai_analysis', {})
        model = ai_config.get('model', 'o3-mini')
        max_companies_per_request = ai_config.get('max_companies_per_request', 8)

        # Get filtering configuration
        filters = self.hub_config.get('filters', {})
        excluded_companies = filters.get('excluded_companies', [])
        recruitment_keywords = filters.get('recruitment_keywords', [])

        # Group companies by city and apply filtering
        cities_data = {}
        total_filtered_companies = 0

        for company_key, company_data in companies_data.items():
            city = company_data['city']
            company_name = company_data['company'].lower()

            # Apply filters
            if any(excluded.lower() in company_name for excluded in excluded_companies):
                logging.info(f"Filtered out excluded company: {company_data['company']}")
                continue

            if any(keyword.lower() in company_name for keyword in recruitment_keywords):
                logging.info(f"Filtered out recruitment company: {company_data['company']}")
                continue

            if city not in cities_data:
                cities_data[city] = {}

            cities_data[city][company_key] = company_data
            total_filtered_companies += 1

        logging.info(f"After filtering: {total_filtered_companies} companies across {len(cities_data)} cities")

        if not cities_data:
            return "No companies found after applying filters."

        # Prepare analysis tasks for parallel processing
        analysis_tasks = []

        for city, city_companies in cities_data.items():
            # If city has too many companies, split into chunks
            company_items = list(city_companies.items())
            if len(company_items) > max_companies_per_request:
                logging.info(f"Splitting {city} ({len(company_items)} companies) into chunks of {max_companies_per_request}")

                for i in range(0, len(company_items), max_companies_per_request):
                    chunk_items = company_items[i:i + max_companies_per_request]
                    chunk_dict = dict(chunk_items)
                    chunk_name = f"{city} (Part {i//max_companies_per_request + 1})"
                    analysis_tasks.append((chunk_dict, chunk_name, model))
            else:
                analysis_tasks.append((city_companies, city, model))

        logging.info(f"Processing {len(analysis_tasks)} analysis tasks in parallel (3 concurrent requests)")

        # Process tasks in parallel batches of 3
        city_analyses = []
        openai_requests_made = 0

        for i in range(0, len(analysis_tasks), 3):
            batch = analysis_tasks[i:i + 3]
            batch_tasks = []

            for companies, city_name, model_name in batch:
                logging.info(f"Analyzing {city_name} with {len(companies)} companies...")
                task = self.analyze_companies_by_city_simplified(companies, city_name, model_name)
                batch_tasks.append((task, city_name))

            # Run batch of up to 3 requests in parallel
            batch_results = await asyncio.gather(*[task for task, _ in batch_tasks])

            # Collect results
            for j, (result, city_name) in enumerate(zip(batch_results, [name for _, name in batch_tasks])):
                city_analyses.append(f"\n=== {city_name.upper()} ===\n{result}")
                openai_requests_made += 1

        logging.info(f"Completed analysis using {openai_requests_made} OpenAI requests")

        # Combine and clean up city analyses - improved consolidation
        city_content_map = {}

        for analysis in city_analyses:
            lines = analysis.split('\n')
            current_city = None

            for line in lines:
                if line.startswith('=== ') and line.endswith(' ==='):
                    # Extract city name and remove part numbers
                    city_header = line.strip('= ')
                    if '(Part' in city_header.upper():
                        current_city = city_header.split(' (Part')[0].strip().upper()
                    elif '(PART' in city_header.upper():
                        current_city = city_header.split(' (PART')[0].strip().upper()
                    else:
                        current_city = city_header.strip().upper()

                    # Initialize city content if not exists
                    if current_city not in city_content_map:
                        city_content_map[current_city] = []

                elif current_city and line.strip():  # Only add non-empty lines
                    # Skip duplicate city headers that might be in the AI response
                    if not (line.strip().startswith('===') and line.strip().endswith('===')):
                        city_content_map[current_city].append(line.strip())

        # Create consolidated city analyses with proper deduplication
        cleaned_analyses = []
        for city, content in city_content_map.items():
            if content:  # Only add cities with content
                # Remove any duplicate lines and empty lines
                unique_content = []
                seen_lines = set()
                for line in content:
                    if line and line not in seen_lines:
                        unique_content.append(line)
                        seen_lines.add(line)

                if unique_content:  # Only add if there's actual content
                    cleaned_analyses.append(f"\n=== {city} ===\n" + '\n'.join(unique_content))

        # Create simple summary
        combined_analysis = "\n".join(cleaned_analyses)
        total_companies = sum(len(companies) for companies in cities_data.values())

        summary = f"""COMPANY OPPORTUNITIES BY CITY
• {total_companies} Target Companies Identified
• {len(cities_data)} Markets Covered
• Focus: Companies with 3+ Technical Positions

{combined_analysis}

Generated on {datetime.now().strftime('%B %d, %Y')}"""

        return summary.strip()

    async def save_analysis_to_db(self, analysis_text: str, stats: Dict, date_range_start: datetime, date_range_end: datetime):
        """
        Save the analysis results to database
        """
        try:
            cursor = self.db_manager.connection.cursor()

            # Create table if it doesn't exist
            create_table_query = """
            CREATE TABLE IF NOT EXISTS hub_simplified_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date_range_start DATETIME NOT NULL,
                date_range_end DATETIME NOT NULL,
                companies_analyzed INT NOT NULL,
                total_positions INT NOT NULL,
                total_jobs INT NOT NULL,
                analysis_text MEDIUMTEXT NOT NULL,
                stats_json JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            cursor.execute(create_table_query)

            # Insert analysis
            insert_query = """
            INSERT INTO hub_simplified_reports (
                date_range_start, date_range_end, companies_analyzed,
                total_positions, total_jobs, analysis_text, stats_json
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(insert_query, (
                date_range_start,
                date_range_end,
                stats['total_companies'],
                stats['total_unique_positions'],
                stats['total_jobs'],
                analysis_text,
                json.dumps(stats)
            ))

            self.db_manager.connection.commit()
            cursor.close()

            logging.info("Analysis saved to database successfully")

        except Exception as e:
            logging.error(f"Failed to save analysis to database: {e}")

    async def save_company_results_to_db(self, companies_data: Dict[str, Dict], date_range_start: datetime, date_range_end: datetime):
        """
        Save individual company results to database for future filtering
        Only called during normal (non-forced) runs
        """
        try:
            cursor = self.db_manager.connection.cursor()

            # Create table if it doesn't exist
            create_table_query = """
            CREATE TABLE IF NOT EXISTS hub_simplified_companies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                company_name VARCHAR(255) NOT NULL,
                city VARCHAR(100) NOT NULL,
                unique_positions INT NOT NULL,
                total_jobs INT NOT NULL,
                source_sites JSON,
                sample_jobs JSON,
                date_range_start DATETIME NOT NULL,
                date_range_end DATETIME NOT NULL,
                reported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_company_city (company_name, city),
                INDEX idx_reported_at (reported_at)
            )
            """
            cursor.execute(create_table_query)

            # Insert each company
            insert_query = """
            INSERT INTO hub_simplified_companies (
                company_name, city, unique_positions, total_jobs,
                source_sites, sample_jobs, date_range_start, date_range_end
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """

            companies_saved = 0
            for company_key, company_data in companies_data.items():
                # Prepare sample jobs data
                sample_jobs = [
                    {
                        'title': job['title'],
                        'location': job['location'],
                        'url': job.get('url', ''),
                        'source_site': job.get('source_site', '')
                    } for job in company_data['jobs'][:5]  # Save up to 5 sample jobs
                ]

                cursor.execute(insert_query, (
                    company_data['company'],
                    company_data['city'],
                    company_data['unique_positions'],
                    company_data['total_jobs'],
                    json.dumps(company_data['source_sites']),
                    json.dumps(sample_jobs),
                    date_range_start,
                    date_range_end
                ))
                companies_saved += 1

            self.db_manager.connection.commit()
            cursor.close()

            logging.info(f"Saved {companies_saved} company results to database for future filtering")

        except Exception as e:
            logging.error(f"Failed to save company results to database: {e}")

    def _format_analysis_for_html(self, analysis: str) -> str:
        """
        Format analysis text for HTML email
        """
        lines = analysis.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue
            elif line.startswith('===') and line.endswith('==='):
                # City headers
                city_name = line.strip('= ')
                formatted_lines.append(f'<h3 style="color: #667eea; margin: 20px 0 10px 0; border-bottom: 1px solid #667eea; padding-bottom: 5px;">{city_name}</h3>')
            elif line.startswith('•'):
                # Bullet points
                formatted_lines.append(f'<p style="margin: 8px 0; padding-left: 20px;">• {line[2:]}</p>')
            else:
                # Regular paragraphs
                formatted_lines.append(f'<p style="margin: 10px 0;">{line}</p>')

        return '\n'.join(formatted_lines)

    async def send_report_email(self, analysis: str, stats: Dict, date_range_start: datetime, date_range_end: datetime):
        """
        Send the analysis report via email
        """
        subject = f"Company List - {stats['total_companies']} Companies - {date_range_end.strftime('%b %d')}"

        # Format analysis for HTML
        formatted_analysis = self._format_analysis_for_html(analysis)

        # Create HTML email content
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1000px; margin: 0 auto; padding: 20px; }}
                .analysis {{ background: white; padding: 30px; border-radius: 10px; }}
                h2 {{ color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; margin-top: 0; }}
                .analysis-content {{ background: #f8f9fa; padding: 20px; border-radius: 5px; line-height: 1.8; }}
                .analysis-content strong {{ color: #667eea; }}
            </style>
        </head>
        <body>
            <div class="analysis">
                <h2>📋 Company List by City</h2>
                <div class="analysis-content">
                    {formatted_analysis}
                </div>
            </div>
        </body>
        </html>
        """

        # Plain text version - clean and focused
        text_content = f"""
COMPANY LIST BY CITY
{'=' * 30}

{analysis}

{'=' * 30}
Generated on {datetime.now().strftime('%B %d, %Y')}
        """

        # Override email content with our custom report
        original_create_html = self.email_sender._create_html_summary
        original_create_text = self.email_sender._create_text_summary

        self.email_sender._create_html_summary = lambda _: html_content
        self.email_sender._create_text_summary = lambda _: text_content

        # Override subject
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        try:
            # Get email recipients from config
            recipients = self.hub_config.get('emails', [self.email_sender.recipient])
            recipients_str = ', '.join(recipients)

            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.email_sender.sender
            msg['To'] = recipients_str

            # Attach parts
            part1 = MIMEText(text_content, 'plain')
            part2 = MIMEText(html_content, 'html')

            msg.attach(part1)
            msg.attach(part2)

            # Send email
            with smtplib.SMTP(self.email_sender.smtp_config['host'], self.email_sender.smtp_config['port']) as server:
                server.starttls()
                server.login(
                    self.email_sender.smtp_config['auth']['user'],
                    self.email_sender.smtp_config['auth']['pass']
                )
                server.send_message(msg)

            logging.info(f"HubSimplified email sent successfully to {recipients_str}")
            return True

        except Exception as e:
            logging.error(f"Failed to send HubSimplified email: {e}")
            return False
        finally:
            # Restore original methods
            self.email_sender._create_html_summary = original_create_html
            self.email_sender._create_text_summary = original_create_text

    async def run_analysis(self, days_back: int = 7, min_jobs: int = 3, force_run: bool = False):
        """
        Run the complete analysis workflow
        """
        try:
            logging.info("Starting HubSimplified analysis...")

            # Calculate date range
            date_range_end = datetime.now()
            date_range_start = date_range_end - timedelta(days=days_back)

            logging.info(f"Analyzing period: {date_range_start.strftime('%Y-%m-%d')} to {date_range_end.strftime('%Y-%m-%d')}")

            # Get companies with multiple job postings
            jobs = await self.get_companies_with_multiple_jobs(days_back, min_jobs)

            if not jobs:
                logging.warning("No companies found with multiple job postings")
                return

            # Group jobs by company and city
            companies_data = self.group_jobs_by_company_and_city(jobs)
            logging.info(f"Found {len(companies_data)} companies for analysis")

            # Generate summary statistics
            stats = self.generate_summary_stats(companies_data)

            # Analyze with AI
            logging.info("Running AI analysis...")
            analysis = await self.analyze_companies_with_ai_simplified(companies_data)

            # Save analysis summary to database (always)
            await self.save_analysis_to_db(analysis, stats, date_range_start, date_range_end)

            # Save individual company results only during normal (non-forced) runs
            if not force_run:
                logging.info("Saving company results for future filtering (normal run)...")
                await self.save_company_results_to_db(companies_data, date_range_start, date_range_end)
            else:
                logging.info("Skipping company results saving (forced run)")

            # Send email report
            logging.info("Sending email report...")
            email_sent = await self.send_report_email(analysis, stats, date_range_start, date_range_end)

            if email_sent:
                logging.info("HubSimplified analysis completed successfully!")
            else:
                logging.warning("Analysis completed but email sending failed")

        except Exception as e:
            logging.error(f"HubSimplified analysis failed: {e}")
            raise
        finally:
            self.db_manager.close()

def should_run_today() -> bool:
    """
    Check if analysis should run today (Mondays)
    """
    return datetime.now().weekday() == 0  # Monday = 0

async def main():
    """
    Main function
    """
    # Check for force run argument
    force_run = '--force' in sys.argv

    if not force_run and not should_run_today():
        logging.info("📅 Not scheduled to run today (runs on Mondays). Use --force to run anyway.")
        return

    try:
        # Load configuration
        config = load_heroku_config()

        # Create analyzer
        analyzer = HubSimplifiedAnalyzer(config)

        # Run analysis
        await analyzer.run_analysis(force_run=force_run)

    except Exception as e:
        logging.error(f"Critical error in HubSimplified: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())


