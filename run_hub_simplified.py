#!/usr/bin/env python3
"""
CLI Runner for HubSimplified - Simple Company List Report
"""

import asyncio
import argparse
import sys
from datetime import datetime
from hubSimplified import HubSimplifiedAnalyzer
from heroku_config import load_heroku_config

async def run_analysis(args):
    """Run the HubSimplified analysis"""
    try:
        config = load_heroku_config()

        print("🚀 Starting HubSimplified analysis...")
        print(f"📅 Analysis period: {args.days} days back")
        print(f"🏢 Minimum positions per company: {args.min_jobs}")

        print("-" * 50)

        # Create analyzer
        analyzer = HubSimplifiedAnalyzer(config)

        # Run analysis
        await analyzer.run_analysis(days_back=args.days, min_jobs=args.min_jobs)

        print("-" * 50)
        print("HubSimplified analysis completed successfully!")

    except Exception as e:
        print(f"Analysis failed: {e}")
        sys.exit(1)

async def test_connection(_args):
    """Test database and email connections"""
    try:
        config = load_heroku_config()

        print("Testing connections...")

        # Test database
        print("Testing database connection...")
        analyzer = HubSimplifiedAnalyzer(config)

        # Simple query to test connection
        cursor = analyzer.db_manager.connection.cursor()
        cursor.execute("SELECT COUNT(*) as job_count FROM jobs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
        result = cursor.fetchone()
        cursor.close()

        print(f"Database connected - Found {result[0]} jobs in last 7 days")

        # Test email configuration
        print("Testing email configuration...")
        if analyzer.email_sender.smtp_config['auth']['pass']:
            print("Email configuration loaded successfully")
        else:
            print("Warning: Email configuration incomplete")

        analyzer.db_manager.close()
        print("✅ All connections tested successfully!")

    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        sys.exit(1)

def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description="HubSimplified - Simple Company List Report",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_hub_simplified.py run                    # Run with default settings (7 days, 3+ positions)
  python run_hub_simplified.py run --days 14          # Analyze last 14 days
  python run_hub_simplified.py run --min-jobs 5       # Require 5+ positions per company
  python run_hub_simplified.py test                   # Test database and email connections
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Run command
    run_parser = subparsers.add_parser('run', help='Run the simplified analysis')
    run_parser.add_argument('--days', type=int, default=7,
                           help='Number of days to look back (default: 7)')
    run_parser.add_argument('--min-jobs', type=int, default=3,
                           help='Minimum unique positions per company (default: 3)')
    run_parser.set_defaults(func=run_analysis)

    # Test command
    test_parser = subparsers.add_parser('test', help='Test database and email connections')
    test_parser.set_defaults(func=test_connection)

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    # Run the selected command
    asyncio.run(args.func(args))
if __name__ == "__main__":
    main()
